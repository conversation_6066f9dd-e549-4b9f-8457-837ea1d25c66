[package]
name = "eterna"
version = "0.1.0"
edition = "2024"

[profile.release]
lto = "fat"              ## Full Link-Time Optimization for better cross-crate inlining and optimization
codegen-units = 1        ## Compile as a single unit for best whole-program optimization
opt-level = 3            ## Optimize for maximum runtime performance
panic = "abort"          ## Avoids unwinding code, slightly improves speed
overflow-checks = false  ## Ensures no runtime checks for integer overflows

[dependencies]
chrono = "0.4.41"
clap = { version = "4.0", features = ["derive"] }
dashmap = "5.5"
dotenv = "0.15"
indexmap = "2.2"
itoa = "1.0"
mysql = "25.0.1"
num-format = "0.4"
once_cell = "1.21.3"
rayon = "1.10"
regex = "1"
serde_json = "1.0.141"

## optionally, add this in .cargo/config.toml
## (or export via RUSTFLAGS) to enable CPU-specific optimizations
# [build]
# rustflags = ["-C", "target-cpu=native"]  # Use all CPU features available on your machine
