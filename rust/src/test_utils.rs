use std::collections::HashMap;
use once_cell::sync::Lazy;

// __Daemon__
// __DHCP__
// __DNS__
// __FilterLog__
// __Router__
// __RouterBoard__
// __Snort__
// __Squid__
// __Switch__
// __UserAudit__
// __UserNotice__
// __UserWarning__
// __VPNServer__
// __VMware__
// __WindowsServer__

#[allow(non_upper_case_globals)]
pub static sensor_list_of_names_and_addresses: Lazy<Vec<String>> = Lazy::new(|| {
    vec![
        "***********".to_string(),
        "***********".to_string(),
        "Sensor-1".to_string(),
        "Sensor-2".to_string(),
    ]
});

#[allow(non_upper_case_globals)]
pub static sensor_dict_of_addresses_and_names: <PERSON>zy<HashMap<String, String>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("***********".to_string(), "Sensor-1".to_string());
    map.insert("***********".to_string(), "Sensor-2".to_string());
    map
});

#[allow(non_upper_case_globals)]
pub static router_list_of_names_and_addresses: Lazy<Vec<String>> = Lazy::new(|| {
    vec![
        "***********".to_string(),
        "***********".to_string(),
        "Router-1".to_string(),
        "Router-2".to_string(),
    ]
});

#[allow(non_upper_case_globals)]
pub static router_dict_of_addresses_and_names: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("***********".to_string(), "Router-1".to_string());
    map.insert("***********".to_string(), "Router-2".to_string());
    map
});

#[allow(non_upper_case_globals)]
pub static routerboard_list_of_names_and_addresses: Lazy<Vec<String>> = Lazy::new(|| {
    vec![
        "***********".to_string(),
        "***********".to_string(),
        "RouterBoard-1".to_string(),
        "RouterBoard-2".to_string(),
    ]
});

#[allow(non_upper_case_globals)]
pub static routerboard_dict_of_addresses_and_names: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("***********".to_string(), "RouterBoard-1".to_string());
    map.insert("***********".to_string(), "RouterBoard-2".to_string());
    map
});

#[allow(non_upper_case_globals)]
pub static windowsserver_list_of_names_and_addresses: Lazy<Vec<String>> = Lazy::new(|| {
    vec![
        "***********".to_string(),
        "***********".to_string(),
        "WindowsServer-1".to_string(),
        "WindowsServer-2".to_string(),
    ]
});

#[allow(non_upper_case_globals)]
pub static windowsserver_dict_of_addresses_and_names: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("***********".to_string(), "WindowsServer-1".to_string());
    map.insert("***********".to_string(), "WindowsServer-2".to_string());
    map
});

#[allow(non_upper_case_globals)]
pub static vmware_list_of_names_and_addresses: Lazy<Vec<String>> = Lazy::new(|| {
    vec![
        "***********".to_string(),
        "***********".to_string(),
        "VMware-1".to_string(),
        "VMware-2".to_string(),
    ]
});

#[allow(non_upper_case_globals)]
pub static vmware_dict_of_addresses_and_names: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("***********".to_string(), "VMware-1".to_string());
    map.insert("***********".to_string(), "VMware-2".to_string());
    map
});

#[allow(non_upper_case_globals)]
pub static switch_list_of_names_and_addresses: Lazy<Vec<String>> = Lazy::new(|| {
    vec![
        "***********".to_string(),
        "***********".to_string(),
        "Switch-1".to_string(),
        "Switch-2".to_string(),
    ]
});

#[allow(non_upper_case_globals)]
pub static switch_dict_of_addresses_and_names: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("***********".to_string(), "Switch-1".to_string());
    map.insert("***********".to_string(), "Switch-2".to_string());
    map
});

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::*;
    use crate::utils_classes::*;
    use crate::utils_parsers::*;
    use std::collections::HashMap;

    #[test]
    fn test_all_values_are_0() {
        assert_eq!(all_values_are_0(&HashMap::from([("a", 0), ("b", 0), ("c", 0)])), true);
        assert_eq!(all_values_are_0(&HashMap::from([("a", 0), ("b", 1), ("c", 0)])), false);
        assert_eq!(all_values_are_0(&HashMap::from([("a", 0), ("b", -1), ("c", 0)])), false);
        assert_eq!(all_values_are_0(&HashMap::<&str, i32>::new()), true);  // empty dictionary
    }

    #[test]
    fn test_hms_to_hourkey() {
        assert_eq!(hms_to_hourkey("12:34:56"), "12:00 - 12:59");
        assert_eq!(hms_to_hourkey("09:15:30"), "09:00 - 09:59");
        assert_eq!(hms_to_hourkey("00:00:00"), "00:00 - 00:59");
        assert_eq!(hms_to_hourkey("23:59:59"), "23:00 - 23:59");

        let result1 = hms_to_hourkey("15:00:00");
        let result2 = hms_to_hourkey("15:30:45");
        let result3 = hms_to_hourkey("15:59:59");

        assert_eq!(&result1, "15:00 - 15:59");
        assert_eq!(&result2, "15:00 - 15:59");
        assert_eq!(&result3, "15:00 - 15:59");
        assert_eq!(&result1, &result2);
        assert_eq!(&result2, &result3);
    }

    #[test]
    fn test_evenly_sized_batches() {
        // test default batch size
        let batches = evenly_sized_batches(25, None);
        assert_eq!(batches.len(), 3);
        assert_eq!(batches[0], vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(batches[1], vec![11, 12, 13, 14, 15, 16, 17, 18, 19, 20]);
        assert_eq!(batches[2], vec![21, 22, 23, 24, 25]);

        // test custom batch size
        let batches = evenly_sized_batches(25, Some(7));
        assert_eq!(batches.len(), 4);
        assert_eq!(batches[0], vec![1, 2, 3, 4, 5, 6, 7]);
        assert_eq!(batches[1], vec![8, 9, 10, 11, 12, 13, 14]);
        assert_eq!(batches[2], vec![15, 16, 17, 18, 19, 20, 21]);
        assert_eq!(batches[3], vec![22, 23, 24, 25]);

        // test batch size larger than total
        let batches = evenly_sized_batches(5, Some(10));
        assert_eq!(batches.len(), 1);
        assert_eq!(batches[0], vec![1, 2, 3, 4, 5]);

        // test zero total length
        let batches = evenly_sized_batches(0, None);
        assert_eq!(batches.len(), 0);

        // test negative total length
        let batches = evenly_sized_batches(-5, None);
        assert_eq!(batches.len(), 0);
    }

    #[test]
    fn test_create_path_of_infile() {
        assert_eq!(
            create_path_of_infile("my_database", "my_table", None),
            "/tmp/infile__my_database__my_table.csv"
        );

        assert_eq!(
            create_path_of_infile("my_database", "my_table", Some(1)),
            "/tmp/infile__my_database__my_table__chunk_1.csv"
        );

        assert_eq!(
            create_path_of_infile("my_database", "my_table", Some(42)),
            "/tmp/infile__my_database__my_table__chunk_42.csv"
        );

        assert_eq!(
            create_path_of_infile("database-name", "table_name", None),
            "/tmp/infile__database-name__table_name.csv"
        );

        assert_eq!(
            create_path_of_infile("daemon__Sensor_1__2024_11_30", "complex-table", None),
            "/tmp/infile__daemon__Sensor_1__2024_11_30__complex-table.csv"
        );

        assert_eq!(
            create_path_of_infile("my_database", "my_table", None),
            "/tmp/infile__my_database__my_table.csv"
        );

    }

    #[test]
    fn test_get_no_of_infiles(){
        // test zero length
        assert_eq!(get_no_of_infiles(0), 0);

        let original_infile_chunksize = if let MYSQLValue::Int(size) = MYSQLConfig::INFILE_CHUNKSIZE.value() {
            size as usize
        } else {
            panic!("Error getting INFILE_CHUNKSIZE from MYSQLConfig");
        };

        // test length less than chunk size
        assert_eq!(get_no_of_infiles(original_infile_chunksize - 1), 1);

        // test length equal to chunk size
        assert_eq!(get_no_of_infiles(original_infile_chunksize), 1);

        // test length greater than chunk size
        assert_eq!(get_no_of_infiles(original_infile_chunksize + 1), 2);
        assert_eq!(get_no_of_infiles(original_infile_chunksize * 19), 19);
    }

    #[test]
    fn test_create_name_of_database() {
        // non-dated databases
        if let MYSQLValue::List(non_dated_dbs) = MYSQLConfig::NON_DATED_DATABASES.value() {
            for ndd in non_dated_dbs {
                assert_eq!(create_name_of_database(&ndd, "", ""), ndd);
                assert_eq!(create_name_of_database(&ndd, "2024-11-30", ""), ndd);
                assert_eq!(create_name_of_database(&ndd, "2024-11-30", "Sensor-1"), ndd);
            }
        }

        // with date only
        assert_eq!(
            create_name_of_database("dhcp", "2024-11-30", ""),
            "dhcp__2024_11_30"
        );
        assert_eq!(
            create_name_of_database("dns", "2023-01-15", ""),
            "dns__2023_01_15"
        );

        // with date and object name
        assert_eq!(
            create_name_of_database("daemon", "2024-11-30", "Sensor-1"),
            "daemon__Sensor_1__2024_11_30"
        );
        assert_eq!(
            create_name_of_database("snort", "2024-11-30", "Sensor-2"),
            "snort__Sensor_2__2024_11_30"
        );
    }

    #[test]
    fn test_create_name_of_index() {
        assert_eq!(create_name_of_index("Domain"), "domain_index");
        assert_eq!(create_name_of_index("Source IP"), "sourceip_index");
        assert_eq!(create_name_of_index("GID:SID"), "gidsid_index");
        assert_eq!(create_name_of_index("User-Agent"), "useragent_index");
        assert_eq!(create_name_of_index("HTTP/1.1"), "http11_index");
        assert_eq!(create_name_of_index("SourceIP"), "sourceip_index");
        assert_eq!(create_name_of_index("CamelCaseKey"), "camelcasekey_index");
        assert_eq!(create_name_of_index(""), "_index");
    }

    #[test]
    fn test_dash_to_underscore() {
        assert_eq!(dash_to_underscore("Sensor-1"), "Sensor_1");
        assert_eq!(dash_to_underscore("2024-11-30"), "2024_11_30");
        assert_eq!(dash_to_underscore("this-is-a-test-string"), "this_is_a_test_string");
        assert_eq!(dash_to_underscore("NoChangeNeeded"), "NoChangeNeeded");
        assert_eq!(dash_to_underscore(""), "");
    }

    #[test]
    fn test_underscore_to_dash() {
        assert_eq!(underscore_to_dash("Sensor_1"), "Sensor-1");
        assert_eq!(underscore_to_dash("2024_11_30"), "2024-11-30");
        assert_eq!(underscore_to_dash("daemon__Sensor_1__2024_11_30"), "daemon--Sensor-1--2024-11-30");
        assert_eq!(underscore_to_dash(""), "");
        assert_eq!(underscore_to_dash("SensorOne"), "SensorOne");
    }

    #[test]
    fn test_normalize_date() {
        // MM/DD/YYYY format
        assert_eq!(normalize_date("12/8/2020"),  "2020-12-08");
        assert_eq!(normalize_date("12/31/2020"), "2020-12-31");
        assert_eq!(normalize_date("01/01/2000"), "2000-01-01");
        assert_eq!(normalize_date("12/31/1999"), "1999-12-31");

        // MM/DD/YY format
        assert_eq!(normalize_date("12/8/20"),  "2020-12-08");
        assert_eq!(normalize_date("12/31/20"), "2020-12-31");
        assert_eq!(normalize_date("01/01/00"), "2000-01-01");
        assert_eq!(normalize_date("12/31/99"), "1999-12-31");

        // invalid format
        assert_eq!(normalize_date("31/12/2020"),   "31/12/2020");
        assert_eq!(normalize_date("2020-12-31"),   "2020-12-31");
        assert_eq!(normalize_date("invalid date"), "invalid date");
        assert_eq!(normalize_date(""),             "");
    }

    #[test]
    fn test_normalize_dns_question_name() {
        assert_eq!(normalize_dns_question_name("example(123)com"),              "example.com");
        assert_eq!(normalize_dns_question_name("(456)example(789)com"),         "example.com");
        assert_eq!(normalize_dns_question_name("example(123)com(456)"),         "example.com");
        assert_eq!(normalize_dns_question_name("sub(1)domain(2)example(3)com"), "sub.domain.example.com");
        assert_eq!(normalize_dns_question_name("example.com"),                  "example.com");
        assert_eq!(normalize_dns_question_name(""),                             "");
        assert_eq!(normalize_dns_question_name("(123)(456)(789)"),              "");
    }

    #[test]
    fn test_normalize_time() {
        assert_eq!(normalize_time("12:00:00 AM"), "00:00:00");
        assert_eq!(normalize_time("01:30:45 AM"), "01:30:45");
        assert_eq!(normalize_time("11:59:59 AM"), "11:59:59");
        assert_eq!(normalize_time("12:00:00 PM"), "12:00:00");
        assert_eq!(normalize_time("01:30:45 PM"), "13:30:45");
        assert_eq!(normalize_time("11:59:59 PM"), "23:59:59");
        assert_eq!(normalize_time("1:15:30 AM"),  "01:15:30");
        assert_eq!(normalize_time("9:45:00 PM"),  "21:45:00");
        assert_eq!(normalize_time("00:00:00 AM"), "00:00:00");
        assert_eq!(normalize_time("13:30:45 PM"), "13:30:45");

        // invalid format
        assert_eq!(normalize_time("25:00:00 PM"), "25:00:00 PM");
        assert_eq!(normalize_time("12:60:00 AM"), "12:60:00 AM");
        assert_eq!(normalize_time("not a time"),  "not a time");
        assert_eq!(normalize_time("12:00:00"),    "12:00:00");
    }

    #[test]
    fn test_is_invalid_ln() {
        assert_eq!(_is_invalid_ln(""), true);
        assert_eq!(_is_invalid_ln("2025-06-21 07:10:12 MSYADDSI (user/notice) [MSWinEventLog       1       N/A     98031276        Sat] Jun 21 07:10:12 2025       N/A     N/A     N/A   N/A      N/A     N/A     N/A             [dns] 6/21/2025 7:10:12 AM 0C20 PACKET  00000294AED611C0 UDP Rcv 192.168.11.29   08a1   Q [0001   D   NOERROR]        (1)h(1)l(1)l(1)l(1-- [ERROR name exceeds safe print buffer length]    N/A"), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (user/notice) [MSWinEventLog    1       N/A     136220     Thu] Jun 12 23:58:47 2025       N/A     N/A     N/A     N/A     N/AN/A     N/A             [dns] 1/12/2025 11:58:47 PM 0FAC PACKET  00000124085DBD40 UDP Rcv *******    4567   Q [0001   D   NOERROR]        (4)ted[ERROR length byte: 0x63]        N/A"), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (user/notice) [MSWinEventLog    1       N/A     135124     Thu] Jun 12 23:22:18 2025       N/A     N/A     N/A     N/A     N/AN/A     N/A             [dns] 1/12/2025 11:22:18 PM 0FA4 PACKET  000001247B3794E0 UDP Rcv *******    4567   Q [0001   D   NOERROR]        (2)1x(49)[ERROR length byte: 0x31 at 000001247B37AE30 leads outside message]   N/A"), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (auth/info) [sshguard] Exiting on signal."), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (auth/info) [sshguard] Now monitoring attacks."), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (auth/alert) [snort] (spp_arpspoof) Unicast ARP request"), true);
        assert_eq!(_is_invalid_ln("FATAL: Cannot open '/var/squid/logs' because it is a directory, not a file."), true);
        assert_eq!(_is_invalid_ln("MIICGDCCAQACAQEwDQYJKoZIhvcNAQELBQAwgZsxCzAJBgNVBAMMAkNBMRcwFQYK"), true);
    }

    // __Daemon__

    #[test]
    fn TestParseLnDaemon__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = DaemonConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 Sensor-1 {} [charon] 05[IKE] <con2|204> activating new tasks",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Daemon,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[charon]");
            assert_eq!(parsed[4], "05[IKE] <con2|204> activating new tasks");
        }
    }

    #[test]
    fn TestParseLnDaemon__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = DaemonConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [charon] 05[IKE] <con2|204> activating new tasks",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Daemon,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[charon]");
            assert_eq!(parsed[4], "05[IKE] <con2|204> activating new tasks");
        }
    }

    #[test]
    fn TestParseLnDaemon__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = DaemonConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Sensor {} [charon] 05[IKE] <con2|204> activating new tasks",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Daemon,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnDaemon__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [charon] 05[IKE] <con2|204> activating new tasks";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Daemon,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnDaemon__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnDaemon__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Daemon,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __DHCP__

    #[test]
    fn TestParseLnDHCP__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = DHCPConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2024-12-08 20:01:30 WindowsServer-1 {} [MSWinEventLog	1	N/A	229692	Sun] Dec 08 20:01:30 2024	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dhcp] 11,12/08/24,20:01:29,Renew,***********,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382E302E30,android-dhcp-8.0.0,,,,0\tN/A",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::DHCP,
                &[],
                &std::collections::HashMap::new(),
            );

            assert_eq!(object_name, None);

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0],  normalize_date("12/08/24"));
            assert_eq!(parsed[1],  "20:01:29");
            assert_eq!(parsed[2],  "11");
            assert_eq!(parsed[3],  "Renew");
            assert_eq!(parsed[4],  "***********");
            assert_eq!(parsed[5],  "xPhone.sth.local");
            assert_eq!(parsed[6],  "38A4EDBA48B9");
            assert_eq!(parsed[7],  "");
            assert_eq!(parsed[8],  "640399471");
            assert_eq!(parsed[9],  "0");
            assert_eq!(parsed[10], "");
            assert_eq!(parsed[11], "");
            assert_eq!(parsed[12], "");
            assert_eq!(parsed[13], "0x616E64726F69642D646863702D382E302E30");
            assert_eq!(parsed[14], "android-dhcp-8.0.0");
            assert_eq!(parsed[15], "");
            assert_eq!(parsed[16], "");
            assert_eq!(parsed[17], "");
            assert_eq!(parsed[18], "0");
        }
    }

    #[test]
    fn TestParseLnDHCP__test_mapping_object_ip_to_object_name() {
        // no-op
    }

    #[test]
    fn TestParseLnDHCP__test_invalid_object_name() {
        // no-op
    }

    #[test]
    fn TestParseLnDHCP__test_invalid_event_type() {
        let ln = "2024-12-08 20:01:30 WindowsServer-1 (invalid-event-type) [MSWinEventLog	1	N/A	229692	Sun] Dec 08 20:01:30 2024	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dhcp] 11,12/08/24,20:01:29,Renew,***********,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382E302E30,android-dhcp-8.0.0,,,,0\tN/A";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::DHCP,
            &[],
            &std::collections::HashMap::new(),
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnDHCP__test_invalid_alert_type() {
        let event_types = if let MYSQLValue::List(types) = DHCPConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2024-12-08 20:01:30 WindowsServer-1 {} [MSWinEventLog	1	N/A	229692	Sun] Dec 08 20:01:30 2024	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[invalid-alert-type] 11,12/08/24,20:01:29,Renew,***********,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382E302E30,android-dhcp-8.0.0,,,,0\tN/A",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::DHCP,
            &[],
            &std::collections::HashMap::new(),
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnDHCP__test_invalid_line() {
        let event_types = if let MYSQLValue::List(types) = DHCPConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();

        let invalid_lines = vec![
            "2023-05-12 23:36:10 This is an invalid line".to_string(),

            // ln has: 'ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name'
            format!(
                "2023-05-12 23:36:10 WindowsServer-1 {} [MSWinEventLog       1       N/A     152263  Thu] Jul 10 00:00:47 2025       N/A     N/A     N/A     N/A     N/A     N/A     N/A             [dhcp] ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name, TransactionID, QResult,Probationtime, CorrelationID,Dhcid,VendorClass(Hex),VendorClass(ASCII),UserClass(Hex),UserClass(ASCII),RelayAgentInformation,DnsRegError. N/A",
                event_type
            ),
        ];
        for ln in invalid_lines {
            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::DHCP,
                &[],
                &std::collections::HashMap::new(),
            );

            assert_eq!(object_name, None);
            assert_eq!(parsed_ln, None);
        };
    }

    // __DNS__

    #[test]
    fn TestParseLnDNS__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = DNSConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2024-12-08 01:16:44 WindowsServer-1 {} [MSWinEventLog     1       N/A     1089080 Sun] Dec 08 01:16:44 2024       N/A     N/A     N/A     N/A   N/A      N/A     N/A             [dns] 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd *************   e942   Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)\tN/A",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::DNS,
                &[],
                &std::collections::HashMap::new(),
            );

            assert_eq!(object_name, None);

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0],  normalize_date("12/8/2024"));
            assert_eq!(parsed[1],  normalize_time("1:16:44 AM"));
            assert_eq!(parsed[2],  "0B54");
            assert_eq!(parsed[3],  "PACKET");
            assert_eq!(parsed[4],  "000001D4F30A0C90");
            assert_eq!(parsed[5],  "UDP");
            assert_eq!(parsed[6],  "Snd");
            assert_eq!(parsed[7],  "*************");
            assert_eq!(parsed[8],  "e942");
            assert_eq!(parsed[9],  "");
            assert_eq!(parsed[10], "Q");
            assert_eq!(parsed[11], "0001");
            assert_eq!(parsed[12], "D");
            assert_eq!(parsed[13], "NOERROR");
            assert_eq!(parsed[14], "AAAA");
            assert_eq!(parsed[15], normalize_dns_question_name("(9)speedtest(10)example(2)AaBbCc(2)id(0)").to_lowercase());
        }
    }

    #[test]
    fn TestParseLnDNS__test_mapping_object_ip_to_object_name() {
        // no-op
    }

    #[test]
    fn TestParseLnDNS__test_invalid_object_name() {
        // no-op
    }

    #[test]
    fn TestParseLnDNS__test_invalid_event_type() {
        let ln = "2024-12-08 01:16:44 WindowsServer-1 (invalid-event-type) [MSWinEventLog     1       N/A     1089080 Sun] Dec 08 01:16:44 2024       N/A     N/A     N/A     N/A   N/A      N/A     N/A             [dns] 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd *************   e942   Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)\tN/A";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::DNS,
            &[],
            &std::collections::HashMap::new(),
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnDNS__test_invalid_alert_type() {
        let event_types = if let MYSQLValue::List(types) = DNSConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2024-12-08 01:16:44 WindowsServer-1 {} [MSWinEventLog     1       N/A     1089080 Sun] Dec 08 01:16:44 2024       N/A     N/A     N/A     N/A   N/A      N/A     N/A             [invalid-alert-type] 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd *************   e942   Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)\tN/A",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::DNS,
            &[],
            &std::collections::HashMap::new(),
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnDNS__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::DNS,
            &[],
            &std::collections::HashMap::new(),
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __FilterLog__

    #[test]
    fn TestParseLnFilterLog__test_valid_line_ipv4_tcp() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,***********00,*******,12345,443,40,S,12345,0,65535,,,,";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::FilterLog,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("Sensor-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0],  "2023-05-12");
        assert_eq!(parsed[1],  "23:36:10");
        assert_eq!(parsed[2],  "tcp");
        assert_eq!(parsed[3],  "***********00");
        assert_eq!(parsed[4],  "*******");
        assert_eq!(parsed[5],  "12345");
        assert_eq!(parsed[6],  "443");
        assert_eq!(parsed[7],  "1000006862");
        assert_eq!(parsed[8],  "pppoe0");
        assert_eq!(parsed[9],  "match");
        assert_eq!(parsed[10], "pass");
        assert_eq!(parsed[11], "out");
    }

    #[test]
    fn TestParseLnFilterLog__test_valid_line_ipv6_udp() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,6,0x0,0,64,udp,17,80,2001:db8::1,2001:db8::2,12345,53,40";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::FilterLog,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("Sensor-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0],  "2023-05-12");
        assert_eq!(parsed[1],  "23:36:10");
        assert_eq!(parsed[2],  "udp");
        assert_eq!(parsed[3],  "2001:db8::1");
        assert_eq!(parsed[4],  "2001:db8::2");
        assert_eq!(parsed[5],  "12345");
        assert_eq!(parsed[6],  "53");
        assert_eq!(parsed[7],  "1000006862");
        assert_eq!(parsed[8],  "pppoe0");
        assert_eq!(parsed[9],  "match");
        assert_eq!(parsed[10], "pass");
        assert_eq!(parsed[11], "out");
    }

    #[test]
    fn TestParseLnFilterLog__test_mapping_object_ip_to_object_name() {
        let ln = "2023-05-12 23:36:10 *********** (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,***********00,*******,12345,443,40,S,12345,0,65535,,,,";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::FilterLog,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("Sensor-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0],  "2023-05-12");
        assert_eq!(parsed[1],  "23:36:10");
        assert_eq!(parsed[2],  "tcp");
        assert_eq!(parsed[3],  "***********00");
        assert_eq!(parsed[4],  "*******");
        assert_eq!(parsed[5],  "12345");
        assert_eq!(parsed[6],  "443");
        assert_eq!(parsed[7],  "1000006862");
        assert_eq!(parsed[8],  "pppoe0");
        assert_eq!(parsed[9],  "match");
        assert_eq!(parsed[10], "pass");
        assert_eq!(parsed[11], "out");
    }

    #[test]
    fn TestParseLnFilterLog__test_invalid_object_name() {
        let ln = "2023-05-12 23:36:10 Invalid-Sensor (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,***********00,*******,12345,443,40,S,12345,0,65535,,,,";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::FilterLog,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnFilterLog__test_invalid_event_type() {
        // no-op
    }

    #[test]
    fn TestParseLnFilterLog__test_invalid_alert_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (local0/info) [invalid-alert-type] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,***********00,*******,12345,443,40,S,12345,0,65535,,,,";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::FilterLog,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnFilterLog__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::FilterLog,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __Router__

    #[test]
    fn TestParseLnRouter__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = RouterConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 Router-1 {} [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Router,
                &router_list_of_names_and_addresses,
                &router_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Router-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[%LINK-3-UPDOWN]");
            assert_eq!(parsed[4], "Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown");
        }
    }

    #[test]
    fn TestParseLnRouter__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = RouterConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Router,
                &router_list_of_names_and_addresses,
                &router_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Router-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[%LINK-3-UPDOWN]");
            assert_eq!(parsed[4], "Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown");
        }
    }

    #[test]
    fn TestParseLnRouter__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = RouterConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Router {} [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Router,
            &router_list_of_names_and_addresses,
            &router_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnRouter__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Router-1 (invalid-event-type) [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Router,
            &router_list_of_names_and_addresses,
            &router_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnRouter__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnRouter__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Router,
            &router_list_of_names_and_addresses,
            &router_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __RouterBoard__

    #[test]
    fn TestParseLnRouterBoard__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = RouterBoardConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 RouterBoard-1 {} [connection] established from *******, port: 37 to *******",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::RouterBoard,
                &routerboard_list_of_names_and_addresses,
                &routerboard_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("RouterBoard-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[connection]");
            assert_eq!(parsed[4], "established from *******, port: 37 to *******");
        }
    }

    #[test]
    fn TestParseLnRouterBoard__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = RouterBoardConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [connection] established from *******, port: 37 to *******",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::RouterBoard,
                &routerboard_list_of_names_and_addresses,
                &routerboard_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("RouterBoard-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[connection]");
            assert_eq!(parsed[4], "established from *******, port: 37 to *******");
        }
    }

    #[test]
    fn TestParseLnRouterBoard__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = RouterBoardConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-RouterBoard {} [connection] established from *******, port: 37 to *******",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::RouterBoard,
            &routerboard_list_of_names_and_addresses,
            &routerboard_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnRouterBoard__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 RouterBoard-1 (invalid-event-type) [connection] established from *******, port: 37 to *******";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::RouterBoard,
            &routerboard_list_of_names_and_addresses,
            &routerboard_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnRouterBoard__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnRouterBoard__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::RouterBoard,
            &routerboard_list_of_names_and_addresses,
            &routerboard_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __Snort__

    #[test]
    fn TestParseLnSnort__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = SnortConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 Sensor-1 {} [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {{TCP}} *******:94 -> *******:32",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Snort,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0],  "2023-05-12");
            assert_eq!(parsed[1],  "23:36:10");
            assert_eq!(parsed[2],  "1:1448:20");
            assert_eq!(parsed[3],  "POLICY-OTHER Microsoft");
            assert_eq!(parsed[4],  "Generic Command");
            assert_eq!(parsed[5],  "3");
            assert_eq!(parsed[6],  "TCP");
            assert_eq!(parsed[7],  "*******");
            assert_eq!(parsed[8],  "94");
            assert_eq!(parsed[9],  "*******");
            assert_eq!(parsed[10], "32");
        }
    }

    #[test]
    fn TestParseLnSnort__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = SnortConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {{TCP}} *******:94 -> *******:32",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Snort,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0],  "2023-05-12");
            assert_eq!(parsed[1],  "23:36:10");
            assert_eq!(parsed[2],  "1:1448:20");
            assert_eq!(parsed[3],  "POLICY-OTHER Microsoft");
            assert_eq!(parsed[4],  "Generic Command");
            assert_eq!(parsed[5],  "3");
            assert_eq!(parsed[6],  "TCP");
            assert_eq!(parsed[7],  "*******");
            assert_eq!(parsed[8],  "94");
            assert_eq!(parsed[9],  "*******");
            assert_eq!(parsed[10], "32");
        }
    }

    #[test]
    fn TestParseLnSnort__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = SnortConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Sensor {} [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {{TCP}} *******:94 -> *******:32",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Snort,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSnort__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} *******:94 -> *******:32";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Snort,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSnort__test_invalid_alert_type() {
        let event_types = if let MYSQLValue::List(types) = SnortConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Sensor-1 {} [invalid-alert-type] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {{TCP}} *******:94 -> *******:32",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Snort,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSnort__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Snort,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __Squid__

    #[test]
    fn TestParseLnSquid__test_valid_line() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (local4/info) [(squid-1)] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Squid,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("Sensor-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0],  "2023-05-12");
        assert_eq!(parsed[1],  "23:36:10");
        assert_eq!(parsed[2],  "0");
        assert_eq!(parsed[3],  "***********");
        assert_eq!(parsed[4],  "NONE_NONE");
        assert_eq!(parsed[5],  "503");
        assert_eq!(parsed[6],  "4101");
        assert_eq!(parsed[7],  "GET");
        assert_eq!(parsed[8],  "https://example.org/AaBbCc".to_lowercase());
        assert_eq!(parsed[9],  "-");
        assert_eq!(parsed[10], "HIER_NONE");
        assert_eq!(parsed[11], "-");
        assert_eq!(parsed[12], "text/html");
    }

    #[test]
    fn TestParseLnSquid__test_mapping_object_ip_to_object_name() {
        let ln = "2023-05-12 23:36:10 *********** (local4/info) [(squid-1)] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Squid,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("Sensor-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0],  "2023-05-12");
        assert_eq!(parsed[1],  "23:36:10");
        assert_eq!(parsed[2],  "0");
        assert_eq!(parsed[3],  "***********");
        assert_eq!(parsed[4],  "NONE_NONE");
        assert_eq!(parsed[5],  "503");
        assert_eq!(parsed[6],  "4101");
        assert_eq!(parsed[7],  "GET");
        assert_eq!(parsed[8],  "https://example.org/AaBbCc".to_lowercase());
        assert_eq!(parsed[9],  "-");
        assert_eq!(parsed[10], "HIER_NONE");
        assert_eq!(parsed[11], "-");
        assert_eq!(parsed[12], "text/html");
    }

    #[test]
    fn TestParseLnSquid__test_invalid_object_name() {
        let ln = "2023-05-12 23:36:10 Invalid-Sensor (local4/info) [(squid-1)] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Squid,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSquid__test_invalid_event_type() {
        // no-op
    }

    #[test]
    fn TestParseLnSquid__test_invalid_alert_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (local4/info) [invalid-alert-type] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Squid,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSquid__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Squid,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __Switch__

    #[test]
    fn TestParseLnSwitch__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = SwitchConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 Switch-1 {} [%SFF8472-5-THRESHOLD_VIOLATION] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Switch,
                &switch_list_of_names_and_addresses,
                &switch_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Switch-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[%SFF8472-5-THRESHOLD_VIOLATION]");
            assert_eq!(parsed[4], "Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)");
        }
    }

    #[test]
    fn TestParseLnSwitch__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = SwitchConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [%SFF8472-5-THRESHOLD_VIOLATION] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Switch,
                &switch_list_of_names_and_addresses,
                &switch_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Switch-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[%SFF8472-5-THRESHOLD_VIOLATION]");
            assert_eq!(parsed[4], "Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)");
        }
    }

    #[test]
    fn TestParseLnSwitch__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = SwitchConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Switch {} [%SFF8472-5-THRESHOLD_VIOLATION] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Switch,
            &switch_list_of_names_and_addresses,
            &switch_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSwitch__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Switch-1 (invalid-event-type) [%SFF8472-5-THRESHOLD_VIOLATION] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Switch,
            &switch_list_of_names_and_addresses,
            &switch_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSwitch__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnSwitch__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Switch,
            &switch_list_of_names_and_addresses,
            &switch_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __UserAudit__

    #[test]
    fn TestParseLnUserAudit__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = UserAuditConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            // JUMP_1
            let messages = [
                "/index.php: Successful login for user 'admin' from: *********** (Local Database)",
                "/index.php: User logged out for user 'admin' from: *********** (Local Database)",
            ];
            for message in messages {
                let ln = format!(
                    "2023-05-12 23:36:10 Sensor-1 {} [php-fpm] {}",
                    e_t,
                    message,
                );

                let (object_name, parsed_ln) = parse_ln(
                    &ln,
                    ConfigType::UserAudit,
                    &sensor_list_of_names_and_addresses,
                    &sensor_dict_of_addresses_and_names,
                );

                assert_eq!(object_name, Some("Sensor-1".to_string()));

                let Some(parsed) = parsed_ln else {
                    panic!("Expected parsed_ln to be Some, but got None");
                };
                assert_eq!(parsed[0], "2023-05-12");
                assert_eq!(parsed[1], "23:36:10");
                assert_eq!(parsed[2], "[php-fpm]");
                assert_eq!(parsed[3], message);
            }
        }
    }

    #[test]
    fn TestParseLnUserAudit__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = UserAuditConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let messages = [
                "/index.php: Successful login for user 'admin' from: *********** (Local Database)",
                "/index.php: User logged out for user 'admin' from: *********** (Local Database)",
            ];
            for message in messages {
                let ln = format!(
                    "2023-05-12 23:36:10 *********** {} [php-fpm] {}",
                    e_t,
                    message,
                );

                let (object_name, parsed_ln) = parse_ln(
                    &ln,
                    ConfigType::UserAudit,
                    &sensor_list_of_names_and_addresses,
                    &sensor_dict_of_addresses_and_names,
                );

                assert_eq!(object_name, Some("Sensor-1".to_string()));

                let Some(parsed) = parsed_ln else {
                    panic!("Expected parsed_ln to be Some, but got None");
                };
                assert_eq!(parsed[0], "2023-05-12");
                assert_eq!(parsed[1], "23:36:10");
                assert_eq!(parsed[2], "[php-fpm]");
                assert_eq!(parsed[3], message);
            }
        }
    }

    #[test]
    fn TestParseLnUserAudit__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = UserAuditConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Sensor {} [php-fpm] /index.php: Successful login for user 'admin' from: *********** (Local Database)",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserAudit,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnUserAudit__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [php-fpm] /index.php: Successful login for user 'admin' from: *********** (Local Database)";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserAudit,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnUserAudit__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnUserAudit__test_invalid_line() {
        let event_types = if let MYSQLValue::List(types) = UserAuditConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();

        let invalid_lines = vec![
            "2023-05-12 23:36:10 This is an invalid line".to_string(),

            // 'Successful login' not in ln.
            // this line is a copy of the valid line used in JUMP_1
            // with one difference: 'Successful login' is removed
            // intentionally turning it into an invalid line.
            format!(
                "2023-05-12 23:36:10 Sensor-1 {} [php-fpm] /index.php: for user 'admin' from: *********** (Local Database)",
                event_type
            ),

            // 'User logged out' not in ln.
            // this line is a copy of the valid line used in JUMP_1
            // with one difference: 'User logged out' is removed
            // intentionally turning it into an invalid line.
            format!(
                "2023-05-12 23:36:10 Sensor-1 {} [php-fpm] /index.php: for user 'admin' from: *********** (Local Database)",
                event_type
            ),
        ];
        for ln in invalid_lines {
            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::UserAudit,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, None);
            assert_eq!(parsed_ln, None);
        };
    }

    // __UserNotice__

    #[test]
    fn TestParseLnUserNotice__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = UserNoticeConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 Sensor-1 {} [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::UserNotice,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], "someserver");
            assert_eq!(parsed[3], "someuser");
            assert_eq!(parsed[4], "*******");
            assert_eq!(parsed[5], "80");
            assert_eq!(parsed[6], "connecting");
        }
    }

    #[test]
    fn TestParseLnUserNotice__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = UserNoticeConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::UserNotice,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], "someserver");
            assert_eq!(parsed[3], "someuser");
            assert_eq!(parsed[4], "*******");
            assert_eq!(parsed[5], "80");
            assert_eq!(parsed[6], "connecting");
        }
    }

    #[test]
    fn TestParseLnUserNotice__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = UserNoticeConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Sensor {} [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserNotice,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnUserNotice__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserNotice,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnUserNotice__test_invalid_alert_type() {
        let event_types = if let MYSQLValue::List(types) = UserNoticeConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Sensor-1 {} [invalid-alert-type] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserNotice,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnUserNotice__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserNotice,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __UserWarning__

    #[test]
    fn TestParseLnUserWarning__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = UserWarningConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 Sensor-1 {} [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::UserWarning,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], "[dpinger]");
            assert_eq!(parsed[3], "GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%");
        }
    }

    #[test]
    fn TestParseLnUserWarning__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = UserWarningConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::UserWarning,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], "[dpinger]");
            assert_eq!(parsed[3], "GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%");
        }
    }

    #[test]
    fn TestParseLnUserWarning__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = UserWarningConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Sensor {} [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserWarning,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnUserWarning__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserWarning,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnUserWarning__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnUserWarning__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::UserWarning,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __VPNServer__

    #[test]
    fn TestParseLnVPNServer__test_valid_line() {
        let ln = r"2023-05-12 23:36:10 VPNSERVER (user/info) [MSWinEventLog	1	System	5061875	Mon] Jun 30 20:28:56 2025       20272   RemoteAccess    N/A     N/A     Information     VPNSERVER.sth.local     N/A         RoutingDomainID- {********-0000-0000-0000-************}: CoID={NA}: The user MYDOMAIN\n.peterson connected on port VPN1-123 on 6/30/2025 at 5:36 PM and disconnected on 6/30/2025 at 8:28 PM.  The user was active for 172 minutes 12 seconds.  ******** bytes were sent and 7613881 bytes were received. The reason for disconnecting was user request. The tunnel used was WAN Miniport (PPTP). The quarantine state was .      654321";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::VPNServer,
            &[],
            &std::collections::HashMap::new(),
        );

        assert_eq!(object_name, None);

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0], "2023-05-12");
        assert_eq!(parsed[1], "23:36:10");
        assert_eq!(parsed[2], "MYDOMAIN");
        assert_eq!(parsed[3], "n.peterson");
        assert_eq!(parsed[4], "VPN1-123");
        assert_eq!(parsed[5], "172 minutes 12 seconds");
        assert_eq!(parsed[6], "********");
        assert_eq!(parsed[7], "7613881");
    }

    #[test]
    fn TestParseLnVPNServer__test_mapping_object_ip_to_object_name() {
        // no-op
    }

    #[test]
    fn TestParseLnVPNServer__test_invalid_object_name() {
        // no-op
    }

    #[test]
    fn TestParseLnVPNServer__test_invalid_event_type() {
        // no-op
    }

    #[test]
    fn TestParseLnVPNServer__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnVPNServer__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::VPNServer,
            &[],
            &std::collections::HashMap::new(),
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __VMware__

    #[test]
    fn TestParseLnVMware__test_valid_line() {
        let ln = "2023-05-12 23:36:10 VMware-1 (local0/info) [vsan-health-main] 2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::VMware,
            &vmware_list_of_names_and_addresses,
            &vmware_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("VMware-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0], "2023-05-12");
        assert_eq!(parsed[1], "23:36:10");
        assert_eq!(parsed[2], "(local0/info)");
        assert_eq!(parsed[3], "[vsan-health-main]");
        assert_eq!(parsed[4], "2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:");
    }

    #[test]
    fn TestParseLnVMware__test_mapping_object_ip_to_object_name() {
        let ln = "2023-05-12 23:36:10 *********** (local0/info) [vsan-health-main] 2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::VMware,
            &vmware_list_of_names_and_addresses,
            &vmware_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("VMware-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0], "2023-05-12");
        assert_eq!(parsed[1], "23:36:10");
        assert_eq!(parsed[2], "(local0/info)");
        assert_eq!(parsed[3], "[vsan-health-main]");
        assert_eq!(parsed[4], "2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:");
    }

    #[test]
    fn TestParseLnVMware__test_invalid_object_name() {
        let ln = "2023-05-12 23:36:10 Invalid-VMware (local0/info) [vsan-health-main] 2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::VMware,
            &vmware_list_of_names_and_addresses,
            &vmware_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnVMware__test_invalid_event_type() {
        // no-op
    }

    #[test]
    fn TestParseLnVMware__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnVMware__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::VMware,
            &vmware_list_of_names_and_addresses,
            &vmware_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __WindowsServer__

    #[test]
    fn TestParseLnWindowsServer__test_valid_line__ws_an_ad_pattern() {
        let ln = "2023-05-12 23:36:10 WindowsServer-1 (user/info) [MSWinEventLog	1	Security	317081	Sat] Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::WindowsServer,
            &windowsserver_list_of_names_and_addresses,
            &windowsserver_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("WindowsServer-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0], "2023-05-12");
        assert_eq!(parsed[1], "23:36:10");
        assert_eq!(parsed[2], "[MSWinEventLog	1	Security	317081	Sat]");
        assert_eq!(parsed[3], "Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********".replace("\t", " "));
        assert_eq!(parsed[4], "4634");
        assert_eq!(parsed[5], "Logon/Logoff");
        assert_eq!(parsed[6], "Low");
        assert_eq!(parsed[7], "FooName$");
        assert_eq!(parsed[8], "BarDomain");
        assert_eq!(parsed[9], "");
    }

    #[test]
    fn TestParseLnWindowsServer__test_valid_line__ws_sw_pattern() {
        let ln = "2023-05-12 23:36:10 WindowsServer-1 (user/info) [MSWinEventLog	1	Security	317081	Sat] Dec 07 00:00:22 2024	4634    Microsoft-Windows-Security-Auditing     N/A     N/A     Success Audit      sth.sth.local      Credential Validation           The computer attempted to validate the credentials for an account.    Authentication Package: MICROSOFT_AUTHENTICATION_PACKAGE_V1_0  Logon Account: SOMEADDS$  Source Workstation: SOMEADDS  Error Code: 0x0 4106884";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::WindowsServer,
            &windowsserver_list_of_names_and_addresses,
            &windowsserver_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("WindowsServer-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0], "2023-05-12");
        assert_eq!(parsed[1], "23:36:10");
        assert_eq!(parsed[2], "[MSWinEventLog	1	Security	317081	Sat]");
        assert_eq!(parsed[3], "Dec 07 00:00:22 2024	4634    Microsoft-Windows-Security-Auditing     N/A     N/A     Success Audit      sth.sth.local      Credential Validation           The computer attempted to validate the credentials for an account.    Authentication Package: MICROSOFT_AUTHENTICATION_PACKAGE_V1_0  Logon Account: SOMEADDS$  Source Workstation: SOMEADDS  Error Code: 0x0 4106884".replace("\t", " "));
        assert_eq!(parsed[4], "4634");
        assert_eq!(parsed[5], "Logon/Logoff");
        assert_eq!(parsed[6], "Low");
        assert_eq!(parsed[7], "");
        assert_eq!(parsed[8], "");
        assert_eq!(parsed[9], "SOMEADDS");
    }

    #[test]
    fn TestParseLnWindowsServer__test_mapping_object_ip_to_object_name() {
        let ln = "2023-05-12 23:36:10 *********** (user/info) [MSWinEventLog	1	Security	317081	Sat] Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::WindowsServer,
            &windowsserver_list_of_names_and_addresses,
            &windowsserver_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, Some("WindowsServer-1".to_string()));

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0],  "2023-05-12");
        assert_eq!(parsed[1],  "23:36:10");
        assert_eq!(parsed[2],  "[MSWinEventLog	1	Security	317081	Sat]");
        assert_eq!(parsed[3],  "Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********".replace("\t", " "));
        assert_eq!(parsed[4],  "4634");
        assert_eq!(parsed[5],  "Logon/Logoff");
        assert_eq!(parsed[6],  "Low");
        assert_eq!(parsed[7],  "FooName$");
        assert_eq!(parsed[8],  "BarDomain");
        assert_eq!(parsed[9],  "");
    }

    #[test]
    fn TestParseLnWindowsServer__test_invalid_object_name() {
        let ln = "2023-05-12 23:36:10 Invalid-WindowsServer (user/info) [MSWinEventLog	1	Security	317081	Sat] Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::WindowsServer,
            &windowsserver_list_of_names_and_addresses,
            &windowsserver_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnWindowsServer__test_invalid_event_type() {
        // no-op
    }

    #[test]
    fn TestParseLnWindowsServer__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnWindowsServer__test_invalid_line() {
        let invalid_lines = vec![
            "2023-05-12 23:36:10 This is an invalid line".to_string(),

            // ln has [dns]
            "2023-05-12 23:36:10 WindowsServer-1 (user/notice) [MSWinEventLog    1       N/A     10044897        Sat] Jul 12 00:00:22 2025       N/A     N/A     N/A     N/A     N/A     N/A     N/A             [dns] 7/12/2025 12:00:22 AM 1090 PACKET  00000163D7FB3D40 UDP Rcv *******    ee38   Q [1000       NOERROR] A      (8)sth(10)example(2)ir(0)     N/A".to_string(),

            // ln has [dhcp]
            "2023-05-12 23:36:10 WindowsServer-1 (user/notice) [MSWinEventLog       1       N/A     206882  Sat] Jul 12 12:52:41 2025       N/A     N/A     N/A     N/A     N/A     N/A     N/A             [dhcp] 31,07/12/25,12:52:41,DNS Update Failed,***********,sth.sth.local,,,0,6,,,,,,,,,9004       N/A".to_string(),
        ];
        for ln in invalid_lines {
            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::WindowsServer,
                &windowsserver_list_of_names_and_addresses,
                &windowsserver_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, None);
            assert_eq!(parsed_ln, None);
        };
    }
}
